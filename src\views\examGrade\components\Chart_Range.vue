<template>
  <div class="ChartRange" ref="ChartRange"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartRange',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },

  methods: {
    init(data) {
      if (!data.length) {
        return
      }
      // 提取班级名称
      const classTypes = Object.keys(data[0]).filter((key) => key !== 'type')
      const colors = ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72']

      // 创建 ECharts 系列数据
      const seriesData = classTypes.map((classType, index) => ({
        name: classType,
        type: 'bar',
        data: data.map((item) => item[classType]),
        barWidth: 30,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors[index] }, // 顶部颜色
            { offset: 1, color: 'rgba(0,145,255,0)' } // 底部颜色透明
          ])
        }
      }))

      var option = {
        title: {
          text: '班级分数分布',
          left: '29px',
          top: '27px',
          textStyle: {
            color: '#293543',
            fontSize: 26,
            fontFamily: 'PingFang SC',
            fontWeight: 400
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          formatter: function (params) {
            let result = `<div>${params[0].axisValue}</div>`
            params.forEach((item) => {
              result += `<div style="margin: 5px 0px;">${item.seriesName}: ${item.value}</div>`
            })
            return result
          },
          textStyle: {
            color: '#FFFFFF', // 设置 tooltip 文字颜色为白色
            fontSize: 16
          }
        },
        grid: {
          left: '120px',
          right: '100px',
          top: '120px',
          bottom: '40px' // 调整网格距离底部的距离
          // 额外配置，确保图形显示完整
        },
        xAxis: {
          type: 'category',
          data: data.map((item) => item.type),
          axisTick: {
            show: false // 刻度标去掉
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16,
            margin: 10 // x轴的文字距离图形的距离
          },
          axisLine: {
            lineStyle: {
              color: '#E3E3E3'
            }
          }
          // 额外配置，确保图形显示完整
        },
        yAxis: {
          type: 'value',
          name: '分', // y轴顶部加一个文字“分”
          nameTextStyle: {
            color: '#999999',
            fontSize: 16,
            padding: [0, 30, 10, 0] // 调整文字位置
          },
          axisLabel: {
            color: '#999999',
            fontSize: 16
          }
          // 额外配置，确保图形显示完整
        },
        series: seriesData
      }
      // 基于准备好的dom，初始化echarts实例并应用配置
      this.chart = echarts.init(this.$refs['ChartRange'])
      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.ChartRange {
  width: 100%;
  height: 100%;
}
</style>
